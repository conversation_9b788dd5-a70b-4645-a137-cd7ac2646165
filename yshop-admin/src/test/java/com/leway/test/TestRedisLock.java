package com.leway.test;

import com.leway.utils.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class TestRedisLock extends AdminServiceTest {
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedisLock redisLock;

    @Test
    public void testRedisLock() {
        String key = "test";
        String clientId = "test";
        redisTemplate.delete(key);

        boolean lock = redisLock.tryLock(key, clientId, 10);
        log.info("lock:{}", lock);
        assertEquals(true, lock, "第一次加锁成功");

        lock = redisLock.tryLock(key, clientId, 1000);
        log.info("lock:{}", lock);
        assertEquals(false, lock, "第二次加锁失败");

        int unlock = redisLock.releaseLock(key, clientId);
        log.info("unlock:{}", unlock);
        assertEquals(0, unlock, "释放锁成功");

        lock = redisLock.tryLock(key, clientId, 1000);
        log.info("lock:{}", lock);
        assertEquals(true, lock, "第三次加锁成功");

        unlock = redisLock.releaseLock(key, clientId);
        log.info("unlock:{}", unlock);
        assertEquals(0, unlock, "释放锁成功");
    }

    /**
     * 测试Redis分布式锁的等待获取功能
     */
    @Test
    public void testTryLockWithWait() throws InterruptedException {
        String lockKey = "test:lock:wait";
        String clientId1 = "client1";
        String clientId2 = "client2";
        redisTemplate.delete(lockKey);

        // 测试1: 正常获取锁
        boolean lock1 = redisLock.tryLockWithWait(lockKey, clientId1, 10, 5);
        assertTrue(lock1, "第一次获取锁应该成功");

        // 测试2: 等待获取锁
        Thread thread = new Thread(() -> {
            try {
                Thread.sleep(2000); // 等待2秒
                redisLock.releaseLock(lockKey, clientId1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        thread.start();

        boolean lock2 = redisLock.tryLockWithWait(lockKey, clientId2, 10, 5);
        assertTrue(lock2, "等待后应该能获取到锁");

        // 测试3: 等待超时
        boolean lock3 = redisLock.tryLockWithWait(lockKey, clientId1, 10, 1);
        assertFalse(lock3, "等待超时应该获取失败");

        // 测试4: 参数验证
        try {
            redisLock.tryLockWithWait(lockKey, clientId1, 10, 0);
            fail("应该抛出IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            // 预期异常
        }

        try {
            redisLock.tryLockWithWait(lockKey, clientId1, 10, 32);
            fail("应该抛出IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            // 预期异常
        }

        String lockValue = redisTemplate.opsForValue().get(lockKey);
        log.info("lockValue:{}", lockValue);
        assertEquals(clientId2, lockValue, "锁值应该为client2");

        // 释放锁
        int errCode = redisLock.releaseLock(lockKey, clientId2);
        assertEquals(0, errCode, "释放锁应该成功");
    }
}
